<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE web-app PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.2//EN" "http://java.sun.com/j2ee/dtds/web-app_2_2.dtd">
<web-app id="tp">
    <display-name>Auberg-Inn - Système de gestion</display-name>
    <description>Application de gestion d'auberge avec MongoDB</description>

    <!-- Page d'accueil par défaut -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    <!--
    <display-name>Système de gestion de resevation WEB</display-name>
    <welcome-file-list>
        <welcome-file>accueil.jsp</welcome-file>
    </welcome-file-list>
    -->

    <context-param>
        <param-name>exempleParam1</param-name>
        <param-value>valeurDeExempleParam1</param-value>
    </context-param>
    <context-param>
        <param-name>exempleParam2</param-name>
        <param-value>valeurDeExempleParam2</param-value>
    </context-param>
    <servlet>
        <servlet-name>jsp</servlet-name>
        <servlet-class>org.apache.jasper.servlet.JspServlet</servlet-class>
        <init-param>
            <param-name>fork</param-name>
            <param-value>false</param-value>
        </init-param>
        <init-param>
            <param-name>xpoweredBy</param-name>
            <param-value>false</param-value>
        </init-param>
        <init-param>
            <param-name>javaEncoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <load-on-startup>3</load-on-startup>
    </servlet>
    <listener>

        <listener-class>com.servlet.TP.TpContextListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>Login</servlet-name>
        <servlet-class>com.servlet.TP.Login</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>Login</servlet-name>
        <url-pattern>/Login</url-pattern>

    </servlet-mapping>

    <!-- Servlet pour la gestion des chambres -->
    <servlet>
        <servlet-name>chambres</servlet-name>
        <servlet-class>com.servlet.TP.ChambreServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>chambres</servlet-name>
        <url-pattern>/chambres</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>client</servlet-name>
        <servlet-class>com.servlet.TP.ClientServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>client</servlet-name>
        <url-pattern>/client</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ReservationServlet</servlet-name>
        <servlet-class>com.servlet.TP.ReservationServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ReservationServlet</servlet-name>
        <url-pattern>/ReservationServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CommoditeServlet</servlet-name>
        <servlet-class>com.servlet.TP.CommoditeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CommoditeServlet</servlet-name>
        <url-pattern>/CommoditeServlet</url-pattern>
    </servlet-mapping>

</web-app>